import 'dart:developer' as dev;
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_home_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/home_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/report_cubit.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

/// Service for managing cache invalidation and data refresh across the app
/// Provides centralized methods for clearing cache and refreshing data when
/// user account information or site data changes
class CacheInvalidationService {
  static final CacheInvalidationService _instance = CacheInvalidationService._internal();
  factory CacheInvalidationService() => _instance;
  CacheInvalidationService._internal();

  /// Clear account-related cache and refresh data after account updates
  /// This should be called after any account information changes (name, email, phone, etc.)
  Future<void> invalidateAccountCache() async {
    try {
      dev.log('🔄 Invalidating account cache...');

      final apiService = Modular.get<ApiService>();

      // Clear account-related cache endpoints
      await Future.wait([
        apiService.clearCacheForEndpoint('/v3/publishers/me/account'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/avatar'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/sites'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/profile'),
      ]);

      // Refresh account data across all relevant cubits
      await _refreshAccountData();

      dev.log('✅ Account cache invalidated successfully');
    } catch (e) {
      dev.log('❌ Error invalidating account cache: $e');
    }
  }

  /// Clear avatar-specific cache after avatar updates
  Future<void> invalidateAvatarCache(String? oldAvatarUrl) async {
    try {
      dev.log('🔄 Invalidating avatar cache...');

      final apiService = Modular.get<ApiService>();

      final futures = <Future>[
        apiService.clearCacheForEndpoint('/v3/publishers/me/avatar'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/account'),
      ];

      if (oldAvatarUrl != null && oldAvatarUrl.isNotEmpty) {
        futures.add(apiService.removeImageFromCache(oldAvatarUrl));
      }

      await Future.wait(futures);

      // Refresh account data to get new avatar
      await _refreshAccountData();

      dev.log('✅ Avatar cache invalidated successfully');
    } catch (e) {
      dev.log('❌ Error invalidating avatar cache: $e');
    }
  }

  /// Clear site-specific cache and refresh data after site addition or changes
  /// This should be called when new sites are added or site information changes
  Future<void> invalidateSiteCache({int? specificSiteId}) async {
    try {
      dev.log('🔄 Invalidating site cache...');

      final apiService = Modular.get<ApiService>();

      // Clear general site-related endpoints
      await Future.wait([
        apiService.clearCacheForEndpoint('/v3/publishers/me/sites'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/account'),
      ]);

      // Clear specific site cache if provided
      if (specificSiteId != null) {
        await apiService.clearSiteSpecificCache(specificSiteId);
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$specificSiteId/campaigns/count-summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$specificSiteId/categories');
      }

      // Refresh site data and related components
      await _refreshSiteData();

      dev.log('✅ Site cache invalidated successfully');
    } catch (e) {
      dev.log('❌ Error invalidating site cache: $e');
    }
  }

  /// Clear all site-dependent data when switching sites
  /// This ensures fresh data is loaded for the newly selected site
  Future<void> invalidateAllSiteDependentCache(int newSiteId, int? previousSiteId) async {
    try {
      dev.log('🔄 Invalidating all site-dependent cache for site switch...');

      final apiService = Modular.get<ApiService>();

      // Clear cache for both old and new sites
      if (previousSiteId != null && previousSiteId != 0) {
        await apiService.clearSiteSpecificCache(previousSiteId);
      }

      if (newSiteId != 0) {
        await apiService.clearSiteSpecificCache(newSiteId);
      }

      // Clear site-dependent endpoints
      await Future.wait([
        apiService.clearCacheForEndpoint('/v3/publishers/me/reports/daily'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/reports/monthly'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/reports/conversion-summary'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/reports/campaign/chart'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/vouchers'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$newSiteId/campaigns/count-summary'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$newSiteId/campaigns/featured-summary'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$newSiteId/categories'),
      ]);

      // Refresh all site-dependent data
      await _refreshAllSiteDependentData();

      dev.log('✅ All site-dependent cache invalidated successfully');
    } catch (e) {
      dev.log('❌ Error invalidating site-dependent cache: $e');
    }
  }

  /// Clear payment-related cache after payment information updates
  Future<void> invalidatePaymentCache() async {
    try {
      dev.log('🔄 Invalidating payment cache...');

      final apiService = Modular.get<ApiService>();

      await Future.wait([
        apiService.clearCacheForEndpoint('/v3/publishers/me/account'),
        apiService.clearCacheForEndpoint('/v3/publishers/me/payment'),
      ]);

      await _refreshAccountData();

      dev.log('✅ Payment cache invalidated successfully');
    } catch (e) {
      dev.log('❌ Error invalidating payment cache: $e');
    }
  }

  /// Refresh account data across all relevant cubits
  Future<void> _refreshAccountData() async {
    try {
      final accountCubit = Modular.get<AccountCubit>();
      await accountCubit.getAccount();
    } catch (e) {
      dev.log('❌ Error refreshing account data: $e');
    }
  }

  /// Refresh site data and notify all dependent components
  Future<void> _refreshSiteData() async {
    try {
      final accountCubit = Modular.get<AccountCubit>();
      final siteCubit = Modular.get<SiteCubit>();

      // Refresh account data which includes sites
      await accountCubit.getAccount();

      // Reinitialize site cubit with fresh data
      await siteCubit.init();
    } catch (e) {
      dev.log('❌ Error refreshing site data: $e');
    }
  }

  /// Refresh all site-dependent data across the app
  Future<void> _refreshAllSiteDependentData() async {
    try {
      await Future.wait([
        _refreshCampaignData(),
        _refreshHomeData(),
        _refreshReportData(),
      ]);
    } catch (e) {
      dev.log('❌ Error refreshing site-dependent data: $e');
    }
  }

  /// Refresh campaign data
  Future<void> _refreshCampaignData() async {
    try {
      final campaignHomeCubit = Modular.get<CampaignHomeCubit>();
      await campaignHomeCubit.refreshCurrentTabData();
    } catch (e) {
      dev.log('❌ Error refreshing campaign data: $e');
    }
  }

  /// Refresh home data
  Future<void> _refreshHomeData() async {
    try {
      final homeCubit = Modular.get<HomeCubit>();
      await homeCubit.refreshAfterSiteSwitch();
    } catch (e) {
      dev.log('❌ Error refreshing home data: $e');
    }
  }

  /// Refresh report data
  Future<void> _refreshReportData() async {
    try {
      final reportCubit = Modular.get<ReportCubit>();
      await reportCubit.refreshAfterSiteSwitch();
    } catch (e) {
      dev.log('❌ Error refreshing report data: $e');
    }
  }
}
