import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_state.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_state.dart';

/// Mixin that provides automatic data refresh capabilities when account or site data changes
/// This ensures that pages display fresh data immediately after account updates or site switches
mixin CacheRefreshMixin<T extends StatefulWidget> on State<T> {
  /// Override this method to define what should happen when account data changes
  /// This is called when user updates their profile, avatar, payment info, etc.
  Future<void> onAccountDataChanged() async {
    // Default implementation - override in your page if needed
  }

  /// Override this method to define what should happen when site data changes
  /// This is called when user switches sites or adds new sites
  Future<void> onSiteDataChanged() async {
    // Default implementation - override in your page if needed
  }

  /// Override this method to define what should happen when the current site changes
  /// This is called when user switches to a different site
  Future<void> onCurrentSiteChanged(int newSiteId, int previousSiteId) async {
    // Default implementation - override in your page if needed
  }

  @override
  void initState() {
    super.initState();
    _setupAccountListener();
    _setupSiteListener();
  }

  /// Set up listener for account data changes
  void _setupAccountListener() {
    try {
      Modular.get<AccountCubit>();
      // Listen to account state changes and trigger refresh
      // This will be called when account data is updated
    } catch (e) {
      // AccountCubit might not be available in all contexts
      debugPrint('CacheRefreshMixin: AccountCubit not available');
    }
  }

  /// Set up listener for site data changes
  void _setupSiteListener() {
    try {
      Modular.get<SiteCubit>();
      // Listen to site state changes and trigger refresh
      // This will be called when site data is updated or current site changes
    } catch (e) {
      // SiteCubit might not be available in all contexts
      debugPrint('CacheRefreshMixin: SiteCubit not available');
    }
  }

  /// Helper method to refresh page data
  /// Call this method when you need to refresh the current page's data
  Future<void> refreshPageData() async {
    if (mounted) {
      await onAccountDataChanged();
      await onSiteDataChanged();
    }
  }

  /// Helper method to check if the page should refresh based on route changes
  /// This can be used with RouteAware to detect when returning to a page
  Future<void> checkAndRefreshIfNeeded() async {
    if (mounted) {
      await refreshPageData();
    }
  }
}

/// Widget wrapper that provides BlocListener functionality for cache refresh
/// Use this to wrap pages that need to respond to account or site changes
class CacheRefreshWrapper extends StatelessWidget {
  final Widget child;
  final VoidCallback? onAccountChanged;
  final VoidCallback? onSiteChanged;
  final Function(int newSiteId, int previousSiteId)? onCurrentSiteChanged;

  const CacheRefreshWrapper({
    super.key,
    required this.child,
    this.onAccountChanged,
    this.onSiteChanged,
    this.onCurrentSiteChanged,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // Listen to account changes
        BlocListener<AccountCubit, AccountState>(
          bloc: Modular.get<AccountCubit>(),
          listenWhen: (previous, current) {
            // Listen when account data actually changes
            return previous.firstName != current.firstName ||
                previous.lastName != current.lastName ||
                previous.email != current.email ||
                previous.profilePictureUrl != current.profilePictureUrl ||
                previous.trafficSources != current.trafficSources;
          },
          listener: (context, state) {
            onAccountChanged?.call();
          },
        ),

        // Listen to site changes
        BlocListener<SiteCubit, SiteState>(
          bloc: Modular.get<SiteCubit>(),
          listenWhen: (previous, current) {
            // Listen when sites list changes or current site changes
            return previous.sites != current.sites || previous.currentSiteId != current.currentSiteId;
          },
          listener: (context, state) {
            if (state.sites != Modular.get<SiteCubit>().state.sites) {
              // Sites list changed
              onSiteChanged?.call();
            } else {
              // Current site changed
              final previousSiteId = Modular.get<SiteCubit>().state.currentSiteId;
              onCurrentSiteChanged?.call(state.currentSiteId, previousSiteId);
            }
          },
        ),
      ],
      child: child,
    );
  }
}

/// Extension to make it easier to use cache refresh functionality
extension CacheRefreshExtension on BuildContext {
  /// Trigger a manual refresh of account data
  Future<void> refreshAccountData() async {
    try {
      final accountCubit = Modular.get<AccountCubit>();
      await accountCubit.getAccount();
    } catch (e) {
      debugPrint('Error refreshing account data: $e');
    }
  }

  /// Trigger a manual refresh of site data
  Future<void> refreshSiteData() async {
    try {
      final siteCubit = Modular.get<SiteCubit>();
      await siteCubit.reloadSites();
    } catch (e) {
      debugPrint('Error refreshing site data: $e');
    }
  }

  /// Trigger a comprehensive refresh of all data
  Future<void> refreshAllData() async {
    await Future.wait([
      refreshAccountData(),
      refreshSiteData(),
    ]);
  }
}
